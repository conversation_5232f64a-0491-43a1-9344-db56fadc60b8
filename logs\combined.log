{
  message: 'Storage provider initialized: local',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:33:38'
}
{
  message: 'Using in-memory database for testing',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:33:38'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:33:38'
}
{
  message: 'Using in-memory cache for testing (Redis replacement)',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:33:38'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:33:38'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:33:38'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  timestamp: '2025-08-02 17:34:07',
  level: 'info',
  message: 'GET /production'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.5624',
  timestamp: '2025-08-02 17:34:28',
  level: 'info',
  message: 'GET /api/health'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Database health check failed: pool.connect is not a function',
  stack: 'TypeError: pool.connect is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:31:31\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:34:28'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Redis health check failed: redisClient.ping is not a function',
  stack: 'TypeError: redisClient.ping is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:43:23\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:34:28'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  timestamp: '2025-08-02 17:34:32',
  level: 'info',
  message: 'GET /production'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  timestamp: '2025-08-02 17:34:36',
  level: 'info',
  message: 'GET /'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  timestamp: '2025-08-02 17:34:39',
  level: 'info',
  message: 'GET /'
}
{
  message: 'Storage provider initialized: local',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:34:54'
}
{
  message: 'Using in-memory database for testing',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:34:54'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:34:54'
}
{
  message: 'Using in-memory cache for testing (Redis replacement)',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:34:54'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:34:54'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:34:54'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  timestamp: '2025-08-02 17:35:03',
  level: 'info',
  message: 'GET /api/health'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Database health check failed: pool.connect is not a function',
  stack: 'TypeError: pool.connect is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:31:31\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:35:03'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Redis health check failed: redisClient.ping is not a function',
  stack: 'TypeError: redisClient.ping is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:43:23\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:35:03'
}
{
  message: 'Storage provider initialized: local',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:16'
}
{
  message: 'Using in-memory database for testing',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:16'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:16'
}
{
  message: 'Using in-memory cache for testing (Redis replacement)',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:16'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:16'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:16'
}
{
  message: 'Storage provider initialized: local',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:27'
}
{
  message: 'Using in-memory database for testing',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:27'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:27'
}
{
  message: 'Using in-memory cache for testing (Redis replacement)',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:27'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:27'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:27'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  timestamp: '2025-08-02 17:35:45',
  level: 'info',
  message: 'GET /api/health'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  timestamp: '2025-08-02 17:35:45',
  level: 'info',
  message: 'POST /api/upload'
}
{
  message: 'Created uploads directory',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:35:45'
}
{
  service: 'ecommerce-gallery-generator',
  imageId: 1,
  filename: 'test-product.png',
  size: 67,
  level: 'info',
  message: 'Image uploaded successfully',
  timestamp: '2025-08-02 17:35:45'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  timestamp: '2025-08-02 17:35:45',
  level: 'info',
  message: 'POST /api/gallery/generate'
}
{
  service: 'ecommerce-gallery-generator',
  imageId: 1,
  productDescription: 'Premium wireless headphones with noise cancellation',
  numImages: 2,
  level: 'info',
  message: 'Starting gallery generation process',
  timestamp: '2025-08-02 17:35:45'
}
{
  service: 'ecommerce-gallery-generator',
  imageId: 1,
  level: 'info',
  message: 'Enhancing original image',
  timestamp: '2025-08-02 17:35:45'
}
{
  service: 'ecommerce-gallery-generator',
  imageUrl: '/uploads/05f1c9be-bf63-4035-907e-7c457c64b7a3.png',
  level: 'info',
  message: 'Using mock image enhancement',
  timestamp: '2025-08-02 17:35:45'
}
{
  service: 'ecommerce-gallery-generator',
  imageId: 1,
  level: 'info',
  message: 'Generating marketing prompts',
  timestamp: '2025-08-02 17:35:46'
}
{
  service: 'ecommerce-gallery-generator',
  imageUrl: '/uploads/05f1c9be-bf63-4035-907e-7c457c64b7a3_enhanced.webp',
  productDescription: 'Premium wireless headphones with noise cancellation',
  level: 'info',
  message: 'Using mock prompt generation',
  timestamp: '2025-08-02 17:35:46'
}
{
  service: 'ecommerce-gallery-generator',
  imageId: 1,
  promptCount: 3,
  level: 'info',
  message: 'Generating gallery images',
  timestamp: '2025-08-02 17:35:47'
}
{
  service: 'ecommerce-gallery-generator',
  imageUrl: '/uploads/05f1c9be-bf63-4035-907e-7c457c64b7a3_enhanced.webp',
  prompt: 'Professional product photography of Premium wireless headphones with noise cancellation with clean white background',
  level: 'info',
  message: 'Using mock gallery image generation',
  timestamp: '2025-08-02 17:35:47'
}
{
  service: 'ecommerce-gallery-generator',
  imageUrl: '/uploads/05f1c9be-bf63-4035-907e-7c457c64b7a3_enhanced.webp',
  prompt: 'Lifestyle shot of Premium wireless headphones with noise cancellation in modern minimalist setting',
  level: 'info',
  message: 'Using mock gallery image generation',
  timestamp: '2025-08-02 17:35:49'
}
{
  service: 'ecommerce-gallery-generator',
  imageId: 1,
  generatedCount: 2,
  level: 'info',
  message: 'Gallery generation completed',
  timestamp: '2025-08-02 17:35:51'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  timestamp: '2025-08-02 17:35:51',
  level: 'info',
  message: 'GET /api/gallery/1'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  timestamp: '2025-08-02 17:36:08',
  level: 'info',
  message: 'GET /'
}
{
  message: 'Storage provider initialized: local',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:36:59'
}
{
  message: 'Using in-memory database for testing',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:36:59'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:36:59'
}
{
  message: 'Using in-memory cache for testing (Redis replacement)',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:36:59'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:36:59'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:36:59'
}
{
  message: 'Storage provider initialized: local',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:37:09'
}
{
  message: 'Using in-memory database for testing',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:37:09'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:37:09'
}
{
  message: 'Using in-memory cache for testing (Redis replacement)',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:37:09'
}
{
  message: 'Redis connected successfully',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:37:09'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'ecommerce-gallery-generator',
  timestamp: '2025-08-02 17:37:09'
}
{
  service: 'ecommerce-gallery-generator',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  timestamp: '2025-08-02 17:37:09',
  level: 'info',
  message: 'GET /favicon.ico'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Database health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:31:14)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Redis health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:47:21)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Database health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:31:14)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Redis health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:47:21)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
