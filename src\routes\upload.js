const express = require('express');
const multer = require('multer');
const { StorageManager } = require('../config/storage');
const { getPool } = require('../config/database');
const { validateSchema, sanitizeFilename } = require('../utils/validation');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const Joi = require('joi');

const router = express.Router();
const storageManager = new StorageManager();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760, // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp').split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new AppError(`File type ${file.mimetype} not allowed. Allowed types: ${allowedTypes.join(', ')}`, 400));
    }
  }
});

// Validation schema for upload
const uploadValidationSchema = Joi.object({
  productTitle: Joi.string().min(1).max(255).optional(),
  productDescription: Joi.string().max(2000).optional(),
  category: Joi.string().max(100).optional()
});

/**
 * Upload product image endpoint
 * POST /api/upload
 */
router.post('/', upload.single('image'), validateSchema(uploadValidationSchema), async (req, res, next) => {
  try {
    if (!req.file) {
      throw new AppError('No image file provided', 400);
    }

    const { originalname, buffer, mimetype, size } = req.file;
    const { productTitle, productDescription, category } = req.body;

    // Sanitize filename
    const sanitizedFilename = sanitizeFilename(originalname);

    // Upload file to storage
    const uploadResult = await storageManager.uploadFile(buffer, sanitizedFilename, mimetype);

    // Store metadata in database
    const db = getPool();
    const imageData = {
      original_filename: originalname,
      stored_filename: uploadResult.filename,
      file_size: size,
      mime_type: mimetype,
      storage_url: uploadResult.url,
      enhancement_status: 'pending',
      product_title: productTitle,
      product_description: productDescription,
      category: category
    };

    const savedImage = db.insertImage(imageData);

    logger.info('Image uploaded successfully', {
      imageId: savedImage.id,
      filename: originalname,
      size: size
    });

    res.status(201).json({
      success: true,
      message: 'Image uploaded successfully',
      data: {
        id: savedImage.id,
        originalFilename: savedImage.original_filename,
        url: savedImage.storage_url,
        size: savedImage.file_size,
        mimeType: savedImage.mime_type,
        uploadDate: savedImage.upload_date,
        enhancementStatus: savedImage.enhancement_status
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * Get uploaded image details
 * GET /api/upload/:imageId
 */
router.get('/:imageId', async (req, res, next) => {
  try {
    const { imageId } = req.params;

    if (!imageId || isNaN(parseInt(imageId))) {
      throw new AppError('Invalid image ID', 400);
    }

    const db = getPool();
    const image = db.getImageById(parseInt(imageId));

    if (!image) {
      throw new AppError('Image not found', 404);
    }

    res.json({
      success: true,
      data: {
        id: image.id,
        originalFilename: image.original_filename,
        url: image.storage_url,
        size: image.file_size,
        mimeType: image.mime_type,
        uploadDate: image.upload_date,
        enhancementStatus: image.enhancement_status,
        enhancedUrl: image.enhanced_url,
        productTitle: image.product_title,
        productDescription: image.product_description,
        category: image.category
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
