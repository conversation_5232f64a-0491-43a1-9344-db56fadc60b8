/* This file is generated by glib-genmar<PERSON><PERSON>, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. */
#pragma once

#include <glib-object.h>

G_BEGIN_DECLS

/* VOID:VOID (../gdk-pixbuf-2.42.10/gdk-pixbuf/gdk-pixbuf-marshal.list:25) */
#define _gdk_pixbuf_marshal_VOID__VOID	g_cclosure_marshal_VOID__VOID

/* VOID:INT,INT (../gdk-pixbuf-2.42.10/gdk-pixbuf/gdk-pixbuf-marshal.list:26) */
extern
void _gdk_pixbuf_marshal_VOID__INT_INT (GClosure     *closure,
                                        GValue       *return_value,
                                        guint         n_param_values,
                                        const GValue *param_values,
                                        gpointer      invocation_hint,
                                        gpointer      marshal_data);

/* VOID:INT,INT,INT,INT (../gdk-pixbuf-2.42.10/gdk-pixbuf/gdk-pixbuf-marshal.list:27) */
extern
void _gdk_pixbuf_marshal_VOID__INT_INT_INT_INT (GClosure     *closure,
                                                GValue       *return_value,
                                                guint         n_param_values,
                                                const GValue *param_values,
                                                gpointer      invocation_hint,
                                                gpointer      marshal_data);

/* VOID:POINTER (../gdk-pixbuf-2.42.10/gdk-pixbuf/gdk-pixbuf-marshal.list:28) */
#define _gdk_pixbuf_marshal_VOID__POINTER	g_cclosure_marshal_VOID__POINTER


G_END_DECLS
