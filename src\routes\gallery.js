const express = require('express');
const { getPool } = require('../config/database');
const { validateSchema } = require('../utils/validation');
const { AppError } = require('../middleware/errorHandler');
const A4FService = require('../services/a4fService');
const logger = require('../utils/logger');
const Joi = require('joi');

const router = express.Router();
const a4fService = new A4FService();

// Validation schemas
const galleryGenerationSchema = Joi.object({
  imageId: Joi.number().integer().positive().required(),
  productDescription: Joi.string().min(1).max(2000).default('Premium product'),
  numImages: Joi.number().integer().min(1).max(5).default(3),
  style: Joi.string().valid('modern', 'classic', 'minimalist', 'luxury', 'casual').optional(),
  background: Joi.string().valid('white', 'transparent', 'lifestyle', 'studio').optional()
});

/**
 * Generate gallery images for a product
 * POST /api/gallery/generate
 */
router.post('/generate', validateSchema(galleryGenerationSchema), async (req, res, next) => {
  try {
    const { imageId, productDescription, numImages, style, background } = req.body;

    const db = getPool();
    const originalImage = db.getImageById(imageId);

    if (!originalImage) {
      throw new AppError('Original image not found', 404);
    }

    logger.info('Starting gallery generation process', {
      imageId,
      productDescription,
      numImages
    });

    // Step 1: Enhance the original image (if not already enhanced)
    let enhancedUrl = originalImage.enhanced_url;
    if (!enhancedUrl) {
      logger.info('Enhancing original image', { imageId });

      const enhancementResult = await a4fService.enhanceImage(originalImage.storage_url);

      if (enhancementResult.success) {
        enhancedUrl = enhancementResult.enhancedUrl;

        // Update image record with enhanced URL
        db.updateImage(imageId, {
          enhanced_url: enhancedUrl,
          enhancement_status: 'completed'
        });
      } else {
        throw new AppError('Image enhancement failed', 500);
      }
    }

    // Step 2: Generate marketing prompts
    logger.info('Generating marketing prompts', { imageId });

    const promptResult = await a4fService.generatePrompts(enhancedUrl, productDescription, { numPrompts: numImages });

    if (!promptResult.success) {
      throw new AppError('Prompt generation failed', 500);
    }

    // Store prompts in database
    const promptData = {
      image_id: imageId,
      product_description: productDescription,
      generated_prompts: promptResult.prompts,
      usage_count: 1
    };
    db.insertPrompt(promptData);

    // Step 3: Generate gallery images using the prompts
    logger.info('Generating gallery images', { imageId, promptCount: promptResult.prompts.length });

    const galleryImages = [];
    for (let i = 0; i < Math.min(promptResult.prompts.length, numImages); i++) {
      const prompt = promptResult.prompts[i];

      try {
        const generationResult = await a4fService.generateGalleryImage(enhancedUrl, prompt, {
          style,
          background,
          resolution: '1024x1024'
        });

        if (generationResult.success && generationResult.generatedUrls.length > 0) {
          const galleryImageData = {
            original_image_id: imageId,
            prompt: prompt,
            generated_url: generationResult.generatedUrls[0],
            generation_status: 'completed',
            model_used: generationResult.model,
            generation_time: generationResult.processingTime
          };

          const savedGalleryImage = db.insertGalleryImage(galleryImageData);
          galleryImages.push(savedGalleryImage);
        }
      } catch (error) {
        logger.error('Failed to generate gallery image', { imageId, prompt, error: error.message });
        // Continue with other prompts even if one fails
      }
    }

    logger.info('Gallery generation completed', {
      imageId,
      generatedCount: galleryImages.length
    });

    res.json({
      success: true,
      message: 'Gallery images generated successfully',
      data: {
        originalImageId: imageId,
        enhancedUrl: enhancedUrl,
        prompts: promptResult.prompts,
        galleryImages: galleryImages.map(img => ({
          id: img.id,
          prompt: img.prompt,
          url: img.generated_url,
          model: img.model_used,
          generationTime: img.generation_time
        })),
        totalGenerated: galleryImages.length
      }
    });

  } catch (error) {
    next(error);
  }
});

/**
 * Get gallery images for a product
 * GET /api/gallery/:imageId
 */
router.get('/:imageId', async (req, res, next) => {
  try {
    const { imageId } = req.params;

    if (!imageId || isNaN(parseInt(imageId))) {
      throw new AppError('Invalid image ID', 400);
    }

    const db = getPool();
    const originalImage = db.getImageById(parseInt(imageId));

    if (!originalImage) {
      throw new AppError('Original image not found', 404);
    }

    const galleryImages = db.getGalleryImagesByOriginalId(parseInt(imageId));
    const prompts = db.getPromptByImageId(parseInt(imageId));

    res.json({
      success: true,
      data: {
        originalImage: {
          id: originalImage.id,
          originalFilename: originalImage.original_filename,
          url: originalImage.storage_url,
          enhancedUrl: originalImage.enhanced_url,
          enhancementStatus: originalImage.enhancement_status
        },
        prompts: prompts ? prompts.generated_prompts : [],
        galleryImages: galleryImages.map(img => ({
          id: img.id,
          prompt: img.prompt,
          url: img.generated_url,
          model: img.model_used,
          generationTime: img.generation_time,
          createdAt: img.created_at
        })),
        totalGalleryImages: galleryImages.length
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
