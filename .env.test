# Test Environment Configuration
NODE_ENV=test
PORT=3001

# Test Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_gallery_test
DB_USER=test_user
DB_PASSWORD=test_password
DB_SSL=false

# Test Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# Mock A4F API Configuration
A4F_API_KEY=ddc-a4f-48581067ab534156a855019afb8e615c
A4F_API_BASE_URL=http://localhost:3002
A4F_TIMEOUT=5000
A4F_MAX_RETRIES=1

# Test Cloud Storage Configuration
AWS_ACCESS_KEY_ID=test_access_key
AWS_SECRET_ACCESS_KEY=test_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=test-bucket

# Test Security
JWT_SECRET=test_jwt_secret_key_for_testing_only
BCRYPT_ROUNDS=4

# Test Rate Limiting (more permissive for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Test File Upload Limits
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Test Logging
LOG_LEVEL=error
