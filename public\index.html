<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-commerce Image Gallery Generator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 5px;
            margin: 10px;
        }
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .gallery-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 E-commerce Image Gallery Generator</h1>
        <p style="text-align: center; color: #666;">Upload a product image and generate AI-powered gallery images for your e-commerce store</p>
        
        <div class="upload-section">
            <h3>📤 Upload Product Image</h3>
            <form id="uploadForm">
                <div class="form-group">
                    <label for="imageFile">Product Image:</label>
                    <input type="file" id="imageFile" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="productTitle">Product Title (Optional):</label>
                    <input type="text" id="productTitle" placeholder="e.g., Wireless Bluetooth Headphones">
                </div>
                <div class="form-group">
                    <label for="productDescription">Product Description (Optional):</label>
                    <textarea id="productDescription" rows="3" placeholder="Describe your product in detail..."></textarea>
                </div>
                <div class="form-group">
                    <label for="category">Category (Optional):</label>
                    <select id="category">
                        <option value="">Select category</option>
                        <option value="Electronics">Electronics</option>
                        <option value="Fashion">Fashion</option>
                        <option value="Home">Home & Garden</option>
                        <option value="Sports">Sports & Outdoors</option>
                        <option value="Beauty">Beauty & Personal Care</option>
                        <option value="Books">Books</option>
                        <option value="Toys">Toys & Games</option>
                    </select>
                </div>
                <button type="submit">Upload Image</button>
            </form>
        </div>

        <div id="results" class="results" style="display: none;">
            <h3>📋 Results</h3>
            <div id="status"></div>
            <div id="uploadedImage"></div>
            <div id="gallerySection" style="display: none;">
                <h4>🎨 Generate Gallery Images</h4>
                <div class="form-group">
                    <label for="numImages">Number of Images:</label>
                    <select id="numImages">
                        <option value="1">1 Image</option>
                        <option value="2" selected>2 Images</option>
                        <option value="3">3 Images</option>
                        <option value="4">4 Images</option>
                        <option value="5">5 Images</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="style">Style:</label>
                    <select id="style">
                        <option value="modern" selected>Modern</option>
                        <option value="classic">Classic</option>
                        <option value="minimalist">Minimalist</option>
                        <option value="luxury">Luxury</option>
                        <option value="casual">Casual</option>
                    </select>
                </div>
                <button id="generateBtn" onclick="generateGallery()">Generate Gallery Images</button>
            </div>
            <div id="galleryResults"></div>
        </div>
    </div>

    <script>
        let uploadedImageId = null;

        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            console.log('Form submitted'); // Debug log

            const formData = new FormData();
            const imageFile = document.getElementById('imageFile').files[0];
            const productTitle = document.getElementById('productTitle').value.trim();
            const productDescription = document.getElementById('productDescription').value.trim();
            const category = document.getElementById('category').value;

            console.log('Image file:', imageFile); // Debug log

            if (!imageFile) {
                showStatus('Please select an image file', 'error');
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!allowedTypes.includes(imageFile.type)) {
                showStatus('Please select a valid image file (JPEG, PNG, or WebP)', 'error');
                return;
            }

            // Validate file size (10MB limit)
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (imageFile.size > maxSize) {
                showStatus('File size must be less than 10MB', 'error');
                return;
            }

            formData.append('image', imageFile);
            if (productTitle) formData.append('productTitle', productTitle);
            if (productDescription) formData.append('productDescription', productDescription);
            if (category) formData.append('category', category);

            console.log('Sending upload request...'); // Debug log
            showStatus('Uploading image...', 'info');
            showLoading();

            try {
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                console.log('Response status:', response.status); // Debug log

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Upload failed:', errorText);
                    showStatus(`Upload failed: ${response.status} ${response.statusText}`, 'error');
                    hideLoading();
                    return;
                }

                const result = await response.json();
                console.log('Upload result:', result); // Debug log

                if (result.success) {
                    uploadedImageId = result.data.id;
                    showStatus('Image uploaded successfully!', 'success');
                    displayUploadedImage(result.data);
                    document.getElementById('gallerySection').style.display = 'block';
                } else {
                    showStatus('Upload failed: ' + (result.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Upload error:', error); // Debug log
                showStatus('Upload error: ' + error.message, 'error');
            }

            hideLoading();
        });

        async function generateGallery() {
            if (!uploadedImageId) {
                showStatus('Please upload an image first', 'error');
                return;
            }

            const numImages = document.getElementById('numImages').value;
            const style = document.getElementById('style').value;
            const productDescription = document.getElementById('productDescription').value || 'Premium product';

            showStatus('Generating gallery images...', 'info');
            showLoading();

            try {
                const response = await fetch('/api/gallery/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        imageId: uploadedImageId,
                        productDescription: productDescription || 'Premium product',
                        numImages: parseInt(numImages),
                        style: style,
                        background: 'white'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus('Gallery images generated successfully!', 'success');
                    displayGalleryResults(result.data);
                } else {
                    showStatus('Gallery generation failed: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('Generation error: ' + error.message, 'error');
            }

            hideLoading();
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            document.getElementById('results').style.display = 'block';
        }

        function showLoading() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="loading"><div class="spinner"></div><p>Processing...</p></div>`;
        }

        function hideLoading() {
            // Status will be updated by showStatus
        }

        function displayUploadedImage(data) {
            const uploadedDiv = document.getElementById('uploadedImage');
            uploadedDiv.innerHTML = `
                <h4>📸 Uploaded Image</h4>
                <div style="display: flex; align-items: center; gap: 20px;">
                    <img src="${data.url}" alt="Uploaded image" class="image-preview">
                    <div>
                        <p><strong>Filename:</strong> ${data.originalFilename}</p>
                        <p><strong>Size:</strong> ${(data.size / 1024).toFixed(2)} KB</p>
                        <p><strong>Type:</strong> ${data.mimeType}</p>
                        <p><strong>Status:</strong> ${data.enhancementStatus}</p>
                    </div>
                </div>
            `;
        }

        function displayGalleryResults(data) {
            const resultsDiv = document.getElementById('galleryResults');
            
            let promptsHtml = '<h4>💡 Generated Prompts</h4><ul>';
            data.prompts.forEach(prompt => {
                promptsHtml += `<li>${prompt}</li>`;
            });
            promptsHtml += '</ul>';

            let galleryHtml = '<h4>🖼️ Gallery Images</h4><div class="gallery-grid">';
            data.galleryImages.forEach((image, index) => {
                galleryHtml += `
                    <div class="gallery-item">
                        <img src="${image.url}" alt="Gallery image ${index + 1}" class="image-preview" style="width: 100%;">
                        <p><strong>Prompt:</strong> ${image.prompt}</p>
                        <p><strong>Model:</strong> ${image.model}</p>
                        <p><strong>Generation Time:</strong> ${image.generationTime}ms</p>
                    </div>
                `;
            });
            galleryHtml += '</div>';

            resultsDiv.innerHTML = promptsHtml + galleryHtml;
        }
    </script>
</body>
</html>
