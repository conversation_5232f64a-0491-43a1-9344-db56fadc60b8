// Local file storage for testing
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

// Storage provider enum
const STORAGE_PROVIDERS = {
  LOCAL: 'local',
  AWS_S3: 'aws_s3',
  GOOGLE_CLOUD: 'google_cloud',
  AZURE_BLOB: 'azure_blob'
};

// For testing, we'll use local storage
function getStorageProvider() {
  return STORAGE_PROVIDERS.LOCAL;
}

// Local storage configuration
async function ensureUploadDirectory() {
  const uploadDir = path.join(process.cwd(), 'uploads');
  try {
    await fs.access(uploadDir);
  } catch (error) {
    await fs.mkdir(uploadDir, { recursive: true });
    logger.info('Created uploads directory');
  }
  return uploadDir;
}

// Local storage manager for testing
class StorageManager {
  constructor() {
    this.provider = getStorageProvider();
    this.uploadDir = null;
    logger.info(`Storage provider initialized: ${this.provider}`);
  }

  async initialize() {
    this.uploadDir = await ensureUploadDirectory();
  }

  async uploadFile(buffer, originalFilename, mimeType) {
    try {
      if (!this.uploadDir) {
        await this.initialize();
      }

      // Generate unique filename
      const ext = path.extname(originalFilename);
      const filename = `${uuidv4()}${ext}`;
      const filePath = path.join(this.uploadDir, filename);

      // Write file to local storage
      await fs.writeFile(filePath, buffer);

      // Return file info
      return {
        filename,
        path: filePath,
        url: `/uploads/${filename}`,
        size: buffer.length,
        mimeType
      };
    } catch (error) {
      logger.error('Failed to upload file:', error);
      throw error;
    }
  }

  async deleteFile(filename) {
    try {
      if (!this.uploadDir) {
        await this.initialize();
      }

      const filePath = path.join(this.uploadDir, filename);
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      logger.error('Failed to delete file:', error);
      return false;
    }
  }

  async getFileUrl(filename) {
    return `/uploads/${filename}`;
  }

  async fileExists(filename) {
    try {
      if (!this.uploadDir) {
        await this.initialize();
      }

      const filePath = path.join(this.uploadDir, filename);
      await fs.access(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = {
  StorageManager,
  STORAGE_PROVIDERS,
  getStorageProvider
};
