// Production-ready storage with cloud support
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

// Storage provider enum
const STORAGE_PROVIDERS = {
  LOCAL: 'local',
  AWS_S3: 'aws_s3',
  GOOGLE_CLOUD: 'google_cloud',
  AZURE_BLOB: 'azure_blob'
};

// Determine storage provider based on environment
function getStorageProvider() {
  // Production: Use cloud storage if credentials are available
  if (process.env.NODE_ENV === 'production') {
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY && process.env.AWS_S3_BUCKET) {
      return STORAGE_PROVIDERS.AWS_S3;
    } else if (process.env.GOOGLE_CLOUD_PROJECT_ID && process.env.GCS_BUCKET) {
      return STORAGE_PROVIDERS.GOOGLE_CLOUD;
    } else if (process.env.AZURE_STORAGE_ACCOUNT && process.env.AZURE_STORAGE_KEY && process.env.AZURE_CONTAINER) {
      return STORAGE_PROVIDERS.AZURE_BLOB;
    }
  }

  // Development/Testing: Use local storage
  return STORAGE_PROVIDERS.LOCAL;
}

// Local storage configuration
async function ensureUploadDirectory() {
  const uploadDir = path.join(process.cwd(), 'uploads');
  try {
    await fs.access(uploadDir);
  } catch (error) {
    await fs.mkdir(uploadDir, { recursive: true });
    logger.info('Created uploads directory');
  }
  return uploadDir;
}

// Production-ready storage manager with cloud support
class StorageManager {
  constructor() {
    this.provider = getStorageProvider();
    this.uploadDir = null;
    this.cloudClient = null;
    logger.info(`Storage provider initialized: ${this.provider}`);
  }

  async initialize() {
    if (this.provider === STORAGE_PROVIDERS.LOCAL) {
      this.uploadDir = await ensureUploadDirectory();
    } else {
      await this.initializeCloudStorage();
    }
  }

  async initializeCloudStorage() {
    try {
      switch (this.provider) {
        case STORAGE_PROVIDERS.AWS_S3:
          const AWS = require('aws-sdk');
          AWS.config.update({
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            region: process.env.AWS_REGION || 'us-east-1'
          });
          this.cloudClient = new AWS.S3();
          break;

        case STORAGE_PROVIDERS.GOOGLE_CLOUD:
          const { Storage } = require('@google-cloud/storage');
          const storage = new Storage({
            projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
            keyFilename: process.env.GOOGLE_CLOUD_KEYFILE
          });
          this.cloudClient = storage.bucket(process.env.GCS_BUCKET);
          break;

        case STORAGE_PROVIDERS.AZURE_BLOB:
          const { BlobServiceClient } = require('@azure/storage-blob');
          const blobServiceClient = BlobServiceClient.fromConnectionString(
            `DefaultEndpointsProtocol=https;AccountName=${process.env.AZURE_STORAGE_ACCOUNT};AccountKey=${process.env.AZURE_STORAGE_KEY};EndpointSuffix=core.windows.net`
          );
          this.cloudClient = blobServiceClient.getContainerClient(process.env.AZURE_CONTAINER);
          break;

        default:
          throw new Error(`Unsupported cloud storage provider: ${this.provider}`);
      }
      logger.info(`Cloud storage initialized: ${this.provider}`);
    } catch (error) {
      logger.error('Failed to initialize cloud storage:', error);
      throw error;
    }
  }

  async uploadFile(buffer, originalFilename, mimeType) {
    try {
      if (!this.uploadDir && !this.cloudClient) {
        await this.initialize();
      }

      // Generate unique filename
      const ext = path.extname(originalFilename);
      const filename = `${uuidv4()}${ext}`;

      if (this.provider === STORAGE_PROVIDERS.LOCAL) {
        return await this.uploadToLocal(buffer, filename, mimeType);
      } else {
        return await this.uploadToCloud(buffer, filename, mimeType);
      }
    } catch (error) {
      logger.error('Failed to upload file:', error);
      throw error;
    }
  }

  async uploadToLocal(buffer, filename, mimeType) {
    const filePath = path.join(this.uploadDir, filename);
    await fs.writeFile(filePath, buffer);

    return {
      filename,
      path: filePath,
      url: `/uploads/${filename}`,
      size: buffer.length,
      mimeType
    };
  }

  async uploadToCloud(buffer, filename, mimeType) {
    let url;

    switch (this.provider) {
      case STORAGE_PROVIDERS.AWS_S3:
        const s3Params = {
          Bucket: process.env.AWS_S3_BUCKET,
          Key: filename,
          Body: buffer,
          ContentType: mimeType,
          ACL: 'public-read'
        };
        const s3Result = await this.cloudClient.upload(s3Params).promise();
        url = s3Result.Location;
        break;

      case STORAGE_PROVIDERS.GOOGLE_CLOUD:
        const file = this.cloudClient.file(filename);
        await file.save(buffer, {
          metadata: {
            contentType: mimeType
          },
          public: true
        });
        url = `https://storage.googleapis.com/${process.env.GCS_BUCKET}/${filename}`;
        break;

      case STORAGE_PROVIDERS.AZURE_BLOB:
        const blockBlobClient = this.cloudClient.getBlockBlobClient(filename);
        await blockBlobClient.upload(buffer, buffer.length, {
          blobHTTPHeaders: {
            blobContentType: mimeType
          }
        });
        url = blockBlobClient.url;
        break;

      default:
        throw new Error(`Unsupported cloud storage provider: ${this.provider}`);
    }

    return {
      filename,
      path: null,
      url: url,
      size: buffer.length,
      mimeType
    };
  }

  async deleteFile(filename) {
    try {
      if (!this.uploadDir) {
        await this.initialize();
      }

      const filePath = path.join(this.uploadDir, filename);
      await fs.unlink(filePath);
      return true;
    } catch (error) {
      logger.error('Failed to delete file:', error);
      return false;
    }
  }

  async getFileUrl(filename) {
    return `/uploads/${filename}`;
  }

  async fileExists(filename) {
    try {
      if (!this.uploadDir) {
        await this.initialize();
      }

      const filePath = path.join(this.uploadDir, filename);
      await fs.access(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = {
  StorageManager,
  STORAGE_PROVIDERS,
  getStorageProvider
};
