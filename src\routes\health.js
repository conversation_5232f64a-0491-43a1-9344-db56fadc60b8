const express = require('express');
const { getPool } = require('../config/database');
const { getRedisClient } = require('../config/redis');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Health check endpoint
 * GET /api/health
 */
router.get('/', async (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: new Date().toISOString(),
    services: {
      database: 'unknown',
      redis: 'unknown',
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024 * 100) / 100,
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024 * 100) / 100,
        external: Math.round(process.memoryUsage().external / 1024 / 1024 * 100) / 100
      }
    }
  };

  // Check database connection
  try {
    const pool = getPool();
    if (pool.inMemory) {
      healthCheck.services.database = 'healthy (in-memory)';
    } else {
      const client = await pool.connect();
      await client.query('SELECT 1');
      client.release();
      healthCheck.services.database = 'healthy';
    }
  } catch (error) {
    healthCheck.services.database = 'unhealthy';
    logger.error('Database health check failed:', error);
  }

  // Check Redis connection
  try {
    const redisClient = getRedisClient();
    if (redisClient.inMemory) {
      healthCheck.services.redis = 'healthy (in-memory)';
    } else {
      await redisClient.ping();
      healthCheck.services.redis = 'healthy';
    }
  } catch (error) {
    healthCheck.services.redis = 'unhealthy';
    logger.error('Redis health check failed:', error);
  }

  // Determine overall health status
  const isHealthy = (healthCheck.services.database === 'healthy' || healthCheck.services.database === 'healthy (in-memory)') &&
                   (healthCheck.services.redis === 'healthy' || healthCheck.services.redis === 'healthy (in-memory)');

  const statusCode = isHealthy ? 200 : 503;
  
  res.status(statusCode).json({
    success: isHealthy,
    data: healthCheck
  });
});

/**
 * Detailed health check endpoint
 * GET /api/health/detailed
 */
router.get('/detailed', async (req, res) => {
  const detailedHealth = {
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    services: {},
    system: {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      memory: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    }
  };

  // Database detailed check
  try {
    const pool = getPool();
    const client = await pool.connect();
    const result = await client.query('SELECT version(), current_database(), current_user');
    client.release();
    
    detailedHealth.services.database = {
      status: 'healthy',
      version: result.rows[0].version,
      database: result.rows[0].current_database,
      user: result.rows[0].current_user,
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingConnections: pool.waitingCount
    };
  } catch (error) {
    detailedHealth.services.database = {
      status: 'unhealthy',
      error: error.message
    };
  }

  // Redis detailed check
  try {
    const redisClient = getRedisClient();
    const info = await redisClient.info();
    detailedHealth.services.redis = {
      status: 'healthy',
      info: info
    };
  } catch (error) {
    detailedHealth.services.redis = {
      status: 'unhealthy',
      error: error.message
    };
  }

  res.json({
    success: true,
    data: detailedHealth
  });
});

module.exports = router;
