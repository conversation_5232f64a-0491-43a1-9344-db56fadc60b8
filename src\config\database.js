const logger = require('../utils/logger');

// In-memory storage for testing
let inMemoryStorage = {
  images: [],
  galleryImages: [],
  prompts: [],
  shopifyIntegrations: []
};

let nextId = 1;
let pool;

// Determine if we should use PostgreSQL or in-memory storage
function shouldUsePostgreSQL() {
  return process.env.NODE_ENV === 'production' &&
         process.env.DB_HOST &&
         process.env.DB_USER &&
         process.env.DB_PASSWORD &&
         process.env.DB_NAME;
}

async function connectDatabase() {
  try {
    if (shouldUsePostgreSQL()) {
      return await connectPostgreSQL();
    } else {
      return await connectInMemory();
    }
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw error;
  }
}

async function connectPostgreSQL() {
  const { Pool } = require('pg');

  const dbConfig = {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  };

  pool = new Pool(dbConfig);

  // Test the connection
  const client = await pool.connect();
  await client.query('SELECT NOW()');
  client.release();

  logger.info('PostgreSQL database connection established successfully');

  // Create tables if they don't exist
  await createPostgreSQLTables();

  return pool;
}

async function connectInMemory() {
  logger.info('Using in-memory database for testing');

  // Initialize in-memory storage
  inMemoryStorage = {
    images: [],
    galleryImages: [],
    prompts: [],
    shopifyIntegrations: []
  };

  return { inMemory: true };
}

async function createPostgreSQLTables() {
  const createTablesQuery = `
    -- Images table for storing uploaded image metadata
    CREATE TABLE IF NOT EXISTS images (
      id SERIAL PRIMARY KEY,
      original_filename VARCHAR(255) NOT NULL,
      stored_filename VARCHAR(255) NOT NULL,
      file_size INTEGER NOT NULL,
      mime_type VARCHAR(100) NOT NULL,
      storage_url TEXT NOT NULL,
      upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      enhanced_url TEXT,
      enhancement_status VARCHAR(50) DEFAULT 'pending',
      product_title VARCHAR(255),
      product_description TEXT,
      category VARCHAR(100),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Gallery images table for storing generated gallery images
    CREATE TABLE IF NOT EXISTS gallery_images (
      id SERIAL PRIMARY KEY,
      original_image_id INTEGER REFERENCES images(id) ON DELETE CASCADE,
      prompt TEXT NOT NULL,
      generated_url TEXT NOT NULL,
      generation_status VARCHAR(50) DEFAULT 'completed',
      model_used VARCHAR(100),
      generation_time INTEGER,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Prompts table for caching generated prompts
    CREATE TABLE IF NOT EXISTS prompts (
      id SERIAL PRIMARY KEY,
      image_id INTEGER REFERENCES images(id) ON DELETE CASCADE,
      product_description TEXT,
      generated_prompts JSONB NOT NULL,
      usage_count INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Shopify integrations table for tracking e-commerce updates
    CREATE TABLE IF NOT EXISTS shopify_integrations (
      id SERIAL PRIMARY KEY,
      product_id VARCHAR(255) NOT NULL,
      shop_domain VARCHAR(255) NOT NULL,
      gallery_image_ids INTEGER[] NOT NULL,
      integration_status VARCHAR(50) DEFAULT 'pending',
      error_message TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Create indexes for better performance
    CREATE INDEX IF NOT EXISTS idx_images_upload_date ON images(upload_date);
    CREATE INDEX IF NOT EXISTS idx_gallery_images_original_id ON gallery_images(original_image_id);
    CREATE INDEX IF NOT EXISTS idx_prompts_image_id ON prompts(image_id);
    CREATE INDEX IF NOT EXISTS idx_shopify_product_id ON shopify_integrations(product_id);
  `;

  try {
    await pool.query(createTablesQuery);
    logger.info('PostgreSQL tables created/verified successfully');
  } catch (error) {
    logger.error('Failed to create PostgreSQL tables:', error);
    throw error;
  }
}

// In-memory database operations
function insertImage(imageData) {
  const image = {
    id: nextId++,
    ...imageData,
    upload_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  inMemoryStorage.images.push(image);
  return image;
}

function getImageById(id) {
  return inMemoryStorage.images.find(img => img.id === parseInt(id));
}

function updateImage(id, updates) {
  const index = inMemoryStorage.images.findIndex(img => img.id === parseInt(id));
  if (index !== -1) {
    inMemoryStorage.images[index] = {
      ...inMemoryStorage.images[index],
      ...updates,
      updated_at: new Date().toISOString()
    };
    return inMemoryStorage.images[index];
  }
  return null;
}

function insertGalleryImage(galleryData) {
  const galleryImage = {
    id: nextId++,
    ...galleryData,
    created_at: new Date().toISOString()
  };
  inMemoryStorage.galleryImages.push(galleryImage);
  return galleryImage;
}

function getGalleryImagesByOriginalId(originalImageId) {
  return inMemoryStorage.galleryImages.filter(img => img.original_image_id === parseInt(originalImageId));
}

function insertPrompt(promptData) {
  const prompt = {
    id: nextId++,
    ...promptData,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  inMemoryStorage.prompts.push(prompt);
  return prompt;
}

function getPromptByImageId(imageId) {
  return inMemoryStorage.prompts.find(prompt => prompt.image_id === parseInt(imageId));
}

function getPool() {
  if (shouldUsePostgreSQL() && pool) {
    return {
      inMemory: false,
      query: pool.query.bind(pool),
      insertImage: insertImagePostgreSQL,
      getImageById: getImageByIdPostgreSQL,
      updateImage: updateImagePostgreSQL,
      insertGalleryImage: insertGalleryImagePostgreSQL,
      getGalleryImagesByOriginalId: getGalleryImagesByOriginalIdPostgreSQL,
      insertPrompt: insertPromptPostgreSQL,
      getPromptByImageId: getPromptByImageIdPostgreSQL
    };
  } else {
    return {
      inMemory: true,
      insertImage,
      getImageById,
      updateImage,
      insertGalleryImage,
      getGalleryImagesByOriginalId,
      insertPrompt,
      getPromptByImageId
    };
  }
}

// PostgreSQL database operations
async function insertImagePostgreSQL(imageData) {
  const query = `
    INSERT INTO images (original_filename, stored_filename, file_size, mime_type, storage_url,
                       enhancement_status, product_title, product_description, category)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `;
  const values = [
    imageData.original_filename,
    imageData.stored_filename,
    imageData.file_size,
    imageData.mime_type,
    imageData.storage_url,
    imageData.enhancement_status || 'pending',
    imageData.product_title,
    imageData.product_description,
    imageData.category
  ];

  const result = await pool.query(query, values);
  return result.rows[0];
}

async function getImageByIdPostgreSQL(id) {
  const query = 'SELECT * FROM images WHERE id = $1';
  const result = await pool.query(query, [id]);
  return result.rows[0];
}

async function updateImagePostgreSQL(id, updates) {
  const setClause = Object.keys(updates).map((key, index) => `${key} = $${index + 2}`).join(', ');
  const query = `UPDATE images SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *`;
  const values = [id, ...Object.values(updates)];

  const result = await pool.query(query, values);
  return result.rows[0];
}

async function insertGalleryImagePostgreSQL(galleryData) {
  const query = `
    INSERT INTO gallery_images (original_image_id, prompt, generated_url, generation_status, model_used, generation_time)
    VALUES ($1, $2, $3, $4, $5, $6)
    RETURNING *
  `;
  const values = [
    galleryData.original_image_id,
    galleryData.prompt,
    galleryData.generated_url,
    galleryData.generation_status || 'completed',
    galleryData.model_used,
    galleryData.generation_time
  ];

  const result = await pool.query(query, values);
  return result.rows[0];
}

async function getGalleryImagesByOriginalIdPostgreSQL(originalImageId) {
  const query = 'SELECT * FROM gallery_images WHERE original_image_id = $1 ORDER BY created_at DESC';
  const result = await pool.query(query, [originalImageId]);
  return result.rows;
}

async function insertPromptPostgreSQL(promptData) {
  const query = `
    INSERT INTO prompts (image_id, product_description, generated_prompts, usage_count)
    VALUES ($1, $2, $3, $4)
    RETURNING *
  `;
  const values = [
    promptData.image_id,
    promptData.product_description,
    JSON.stringify(promptData.generated_prompts),
    promptData.usage_count || 1
  ];

  const result = await pool.query(query, values);
  return result.rows[0];
}

async function getPromptByImageIdPostgreSQL(imageId) {
  const query = 'SELECT * FROM prompts WHERE image_id = $1 ORDER BY created_at DESC LIMIT 1';
  const result = await pool.query(query, [imageId]);
  if (result.rows[0]) {
    result.rows[0].generated_prompts = JSON.parse(result.rows[0].generated_prompts);
  }
  return result.rows[0];
}

async function closeDatabase() {
  if (pool) {
    await pool.end();
    logger.info('PostgreSQL database connection closed');
  } else {
    logger.info('In-memory database cleared');
    inMemoryStorage = {
      images: [],
      galleryImages: [],
      prompts: [],
      shopifyIntegrations: []
    };
  }
}

module.exports = {
  connectDatabase,
  getPool,
  closeDatabase
};
