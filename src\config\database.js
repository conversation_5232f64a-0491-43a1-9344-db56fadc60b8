const logger = require('../utils/logger');

// In-memory storage for testing
let inMemoryStorage = {
  images: [],
  galleryImages: [],
  prompts: [],
  shopifyIntegrations: []
};

let nextId = 1;

async function connectDatabase() {
  try {
    logger.info('Using in-memory database for testing');

    // Initialize in-memory storage
    inMemoryStorage = {
      images: [],
      galleryImages: [],
      prompts: [],
      shopifyIntegrations: []
    };

    return { inMemory: true };
  } catch (error) {
    logger.error('Failed to initialize in-memory database:', error);
    throw error;
  }
}

// In-memory database operations
function insertImage(imageData) {
  const image = {
    id: nextId++,
    ...imageData,
    upload_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  inMemoryStorage.images.push(image);
  return image;
}

function getImageById(id) {
  return inMemoryStorage.images.find(img => img.id === parseInt(id));
}

function updateImage(id, updates) {
  const index = inMemoryStorage.images.findIndex(img => img.id === parseInt(id));
  if (index !== -1) {
    inMemoryStorage.images[index] = {
      ...inMemoryStorage.images[index],
      ...updates,
      updated_at: new Date().toISOString()
    };
    return inMemoryStorage.images[index];
  }
  return null;
}

function insertGalleryImage(galleryData) {
  const galleryImage = {
    id: nextId++,
    ...galleryData,
    created_at: new Date().toISOString()
  };
  inMemoryStorage.galleryImages.push(galleryImage);
  return galleryImage;
}

function getGalleryImagesByOriginalId(originalImageId) {
  return inMemoryStorage.galleryImages.filter(img => img.original_image_id === parseInt(originalImageId));
}

function insertPrompt(promptData) {
  const prompt = {
    id: nextId++,
    ...promptData,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  inMemoryStorage.prompts.push(prompt);
  return prompt;
}

function getPromptByImageId(imageId) {
  return inMemoryStorage.prompts.find(prompt => prompt.image_id === parseInt(imageId));
}

function getPool() {
  return {
    inMemory: true,
    insertImage,
    getImageById,
    updateImage,
    insertGalleryImage,
    getGalleryImagesByOriginalId,
    insertPrompt,
    getPromptByImageId
  };
}

async function closeDatabase() {
  logger.info('In-memory database cleared');
  inMemoryStorage = {
    images: [],
    galleryImages: [],
    prompts: [],
    shopifyIntegrations: []
  };
}

module.exports = {
  connectDatabase,
  getPool,
  closeDatabase
};
