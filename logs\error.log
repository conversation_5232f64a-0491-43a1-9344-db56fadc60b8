{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Database health check failed: pool.connect is not a function',
  stack: 'TypeError: pool.connect is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:31:31\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:34:28'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Redis health check failed: redisClient.ping is not a function',
  stack: 'TypeError: redisClient.ping is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:43:23\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:34:28'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Database health check failed: pool.connect is not a function',
  stack: 'TypeError: pool.connect is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:31:31\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:35:03'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: 'Redis health check failed: redisClient.ping is not a function',
  stack: 'TypeError: redisClient.ping is not a function\n' +
    '    at D:\\new\\try\\src\\routes\\health.js:43:23\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)',
  timestamp: '2025-08-02 17:35:03'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Database health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:31:14)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Redis health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:47:21)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Database health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:31:14)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
{
  service: 'ecommerce-gallery-generator',
  level: 'error',
  message: "Redis health check failed: Cannot read properties of undefined (reading 'inMemory')",
  stack: "TypeError: Cannot read properties of undefined (reading 'inMemory')\n" +
    '    at inMemory (D:\\new\\try\\src\\routes\\health.js:47:21)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at Route.dispatch (D:\\new\\try\\node_modules\\express\\lib\\router\\route.js:119:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:284:15\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:47:12)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at next (D:\\new\\try\\src\\app.js:54:3)\n' +
    '    at Layer.handle [as handle_request] (D:\\new\\try\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\serve-static\\index.js:121:7)\n' +
    '    at SendStream.emit (node:events:518:28)\n' +
    '    at SendStream.error (D:\\new\\try\\node_modules\\send\\index.js:270:17)\n' +
    '    at SendStream.onStatError (D:\\new\\try\\node_modules\\send\\index.js:417:12)\n' +
    '    at next (D:\\new\\try\\node_modules\\send\\index.js:730:16)\n' +
    '    at onstat (D:\\new\\try\\node_modules\\send\\index.js:719:14)\n' +
    '    at FSReqCallback.oncomplete (node:fs:197:21)',
  timestamp: '2025-08-02 17:37:37'
}
