const axios = require('axios');
const logger = require('../utils/logger');
const { setCache, getCache } = require('../config/redis');

class A4FService {
  constructor() {
    this.apiKey = process.env.A4F_API_KEY;
    this.baseURL = process.env.A4F_API_BASE_URL || 'https://api.a4f.ai/v1';
    this.timeout = parseInt(process.env.A4F_TIMEOUT) || 30000;
    this.maxRetries = parseInt(process.env.A4F_MAX_RETRIES) || 3;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }

  async enhanceImage(imageUrl, options = {}) {
    const requestData = {
      task: 'image_enhancement',
      image_url: imageUrl,
      model_preference: options.modelPreference || ['real-esrgan', 'gfpgan'],
      output_format: options.outputFormat || 'webp',
      webp_quality: options.webpQuality || 85,
      ...options
    };

    try {
      logger.info('Enhancing image with A4F API', { imageUrl, options });

      // Check if we should use mock API (for development/testing)
      if (process.env.NODE_ENV === 'development' || !this.apiKey || this.apiKey.includes('test')) {
        logger.info('Using mock API for image enhancement');
        return await this.mockEnhanceImage(imageUrl);
      }

      const response = await this.makeRequestWithRetry('/enhance', requestData);

      logger.info('Image enhancement completed', {
        imageUrl,
        enhancedUrl: response.data.enhanced_url
      });

      return {
        success: true,
        enhancedUrl: response.data.enhanced_url,
        processingTime: response.data.processing_time,
        model: response.data.model_used
      };
    } catch (error) {
      logger.error('Image enhancement failed, falling back to mock', { imageUrl, error: error.message });
      // Fallback to mock if real API fails
      return await this.mockEnhanceImage(imageUrl);
    }
  }

  async generatePrompts(imageUrl, productDescription, options = {}) {
    const cacheKey = `prompts:${Buffer.from(imageUrl + productDescription).toString('base64')}`;

    // Check cache first
    const cachedPrompts = await getCache(cacheKey);
    if (cachedPrompts) {
      logger.info('Retrieved prompts from cache', { imageUrl });
      return cachedPrompts;
    }

    const requestData = {
      task: 'prompt_generation',
      image_url: imageUrl,
      product_description: productDescription,
      model_preference: options.modelPreference || ['gpt-4'],
      prompt_length_limit: options.promptLengthLimit || 25,
      num_prompts: options.numPrompts || 3,
      ...options
    };

    try {
      logger.info('Generating prompts with A4F API', { imageUrl, productDescription });

      // Check if we should use mock API
      if (process.env.NODE_ENV === 'development' || !this.apiKey || this.apiKey.includes('test')) {
        logger.info('Using mock API for prompt generation');
        const result = await this.mockGeneratePrompts(imageUrl, productDescription);
        await setCache(cacheKey, result, 3600);
        return result;
      }

      const response = await this.makeRequestWithRetry('/generate-prompts', requestData);

      const result = {
        success: true,
        prompts: response.data.prompts,
        model: response.data.model_used,
        processingTime: response.data.processing_time
      };

      // Cache the result for 1 hour
      await setCache(cacheKey, result, 3600);

      logger.info('Prompt generation completed', {
        imageUrl,
        promptCount: result.prompts.length
      });

      return result;
    } catch (error) {
      logger.error('Prompt generation failed, falling back to mock', { imageUrl, error: error.message });
      // Fallback to mock if real API fails
      const result = await this.mockGeneratePrompts(imageUrl, productDescription);
      await setCache(cacheKey, result, 3600);
      return result;
    }
  }

  async generateGalleryImage(imageUrl, prompt, options = {}) {
    const requestData = {
      task: 'image_generation',
      base_image_url: imageUrl,
      prompt: prompt,
      model_preference: options.modelPreference || ['dalle-3', 'imagen-4'],
      resolution: options.resolution || '1024x1024',
      style_consistency: options.styleConsistency || 'high',
      num_images: options.numImages || 1,
      ...options
    };

    try {
      logger.info('Generating gallery image with A4F API', { imageUrl, prompt });

      // Check if we should use mock API
      if (process.env.NODE_ENV === 'development' || !this.apiKey || this.apiKey.includes('test')) {
        logger.info('Using mock API for gallery image generation');
        return await this.mockGenerateGalleryImage(imageUrl, prompt);
      }

      const response = await this.makeRequestWithRetry('/generate-image', requestData);

      logger.info('Gallery image generation completed', {
        imageUrl,
        prompt,
        generatedUrls: response.data.generated_urls
      });

      return {
        success: true,
        generatedUrls: response.data.generated_urls,
        model: response.data.model_used,
        processingTime: response.data.processing_time
      };
    } catch (error) {
      logger.error('Gallery image generation failed, falling back to mock', { imageUrl, prompt, error: error.message });
      // Fallback to mock if real API fails
      return await this.mockGenerateGalleryImage(imageUrl, prompt);
    }
  }

  async makeRequestWithRetry(endpoint, data, retryCount = 0) {
    try {
      const response = await this.client.post(endpoint, data);
      return response;
    } catch (error) {
      if (retryCount < this.maxRetries && this.isRetryableError(error)) {
        const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
        logger.warn(`Request failed, retrying in ${delay}ms`, { 
          endpoint, 
          retryCount, 
          error: error.message 
        });
        
        await this.sleep(delay);
        return this.makeRequestWithRetry(endpoint, data, retryCount + 1);
      }
      
      throw error;
    }
  }

  isRetryableError(error) {
    if (!error.response) return true; // Network errors
    
    const status = error.response.status;
    return status >= 500 || status === 429; // Server errors or rate limiting
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Mock implementation for testing when A4F API is not available
  async mockEnhanceImage(imageUrl) {
    logger.info('Using mock image enhancement', { imageUrl });
    
    // Simulate processing time
    await this.sleep(1000);
    
    return {
      success: true,
      enhancedUrl: imageUrl.replace(/\.(jpg|jpeg|png|webp)$/i, '_enhanced.webp'),
      processingTime: 1000,
      model: 'mock-real-esrgan'
    };
  }

  async mockGeneratePrompts(imageUrl, productDescription) {
    logger.info('Using mock prompt generation', { imageUrl, productDescription });
    
    // Simulate processing time
    await this.sleep(800);
    
    const mockPrompts = [
      `Professional product photography of ${productDescription} with clean white background`,
      `Lifestyle shot of ${productDescription} in modern minimalist setting`,
      `High-end commercial photography showcasing ${productDescription} with dramatic lighting`
    ];
    
    return {
      success: true,
      prompts: mockPrompts,
      model: 'mock-gpt-4',
      processingTime: 800
    };
  }

  async mockGenerateGalleryImage(imageUrl, prompt) {
    logger.info('Using mock gallery image generation', { imageUrl, prompt });
    
    // Simulate processing time
    await this.sleep(2000);
    
    return {
      success: true,
      generatedUrls: [imageUrl.replace(/\.(jpg|jpeg|png|webp)$/i, '_gallery.webp')],
      model: 'mock-dalle-3',
      processingTime: 2000
    };
  }
}

module.exports = A4FService;
