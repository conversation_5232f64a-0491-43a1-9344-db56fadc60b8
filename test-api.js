const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

async function testHealthEndpoint() {
  try {
    console.log('🔍 Testing health endpoint...');
    const response = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testImageUpload() {
  try {
    console.log('\n📤 Testing image upload...');
    
    // Create a simple test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'test-product.png',
      contentType: 'image/png'
    });
    formData.append('productTitle', 'Test Product');
    formData.append('productDescription', 'A beautiful test product for demonstration');
    formData.append('category', 'Electronics');

    const response = await axios.post(`${BASE_URL}/api/upload`, formData, {
      headers: {
        ...formData.getHeaders()
      }
    });

    console.log('✅ Image upload successful:', response.data);
    return response.data.data.id;
  } catch (error) {
    console.error('❌ Image upload failed:', error.response?.data || error.message);
    return null;
  }
}

async function testGalleryGeneration(imageId) {
  try {
    console.log('\n🎨 Testing gallery generation...');
    
    const requestData = {
      imageId: imageId,
      productDescription: 'Premium wireless headphones with noise cancellation',
      numImages: 2,
      style: 'modern',
      background: 'white',
      useMockApi: true // Use mock API for testing
    };

    const response = await axios.post(`${BASE_URL}/api/gallery/generate`, requestData);
    console.log('✅ Gallery generation successful:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Gallery generation failed:', error.response?.data || error.message);
    return false;
  }
}

async function testGetGallery(imageId) {
  try {
    console.log('\n📋 Testing gallery retrieval...');
    
    const response = await axios.get(`${BASE_URL}/api/gallery/${imageId}`);
    console.log('✅ Gallery retrieval successful:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Gallery retrieval failed:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting API Tests...\n');

  // Test 1: Health check
  const healthOk = await testHealthEndpoint();
  if (!healthOk) {
    console.log('❌ Health check failed, stopping tests');
    return;
  }

  // Test 2: Image upload
  const imageId = await testImageUpload();
  if (!imageId) {
    console.log('❌ Image upload failed, stopping tests');
    return;
  }

  // Test 3: Gallery generation
  const galleryOk = await testGalleryGeneration(imageId);
  if (!galleryOk) {
    console.log('❌ Gallery generation failed');
  }

  // Test 4: Gallery retrieval
  const retrievalOk = await testGetGallery(imageId);
  if (!retrievalOk) {
    console.log('❌ Gallery retrieval failed');
  }

  console.log('\n🎉 API Tests completed!');
  console.log('\n📝 Summary:');
  console.log(`   Health Check: ${healthOk ? '✅' : '❌'}`);
  console.log(`   Image Upload: ${imageId ? '✅' : '❌'}`);
  console.log(`   Gallery Generation: ${galleryOk ? '✅' : '❌'}`);
  console.log(`   Gallery Retrieval: ${retrievalOk ? '✅' : '❌'}`);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
