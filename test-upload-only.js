const axios = require('axios');
const FormData = require('form-data');

const BASE_URL = 'http://localhost:3000';

async function testUploadOnly() {
  try {
    console.log('🔍 Testing upload endpoint only...');
    
    // Create a simple test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'test-product.png',
      contentType: 'image/png'
    });
    formData.append('productTitle', 'Test Product');
    formData.append('productDescription', 'A beautiful test product for demonstration');
    formData.append('category', 'Electronics');

    console.log('Sending upload request...');
    
    const response = await axios.post(`${BASE_URL}/api/upload`, formData, {
      headers: {
        ...formData.getHeaders()
      },
      timeout: 10000
    });

    console.log('✅ Upload successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
    return response.data.data.id;
  } catch (error) {
    console.error('❌ Upload failed!');
    console.error('Error message:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Run test
testUploadOnly().then(imageId => {
  if (imageId) {
    console.log(`\n🎉 Upload test completed successfully! Image ID: ${imageId}`);
  } else {
    console.log('\n❌ Upload test failed');
  }
}).catch(console.error);
