const logger = require('../utils/logger');

// In-memory cache for testing
let inMemoryCache = new Map();
let redisClient;

// Determine if we should use Redis or in-memory cache
function shouldUseRedis() {
  return process.env.NODE_ENV === 'production' && process.env.REDIS_HOST;
}

async function connectRedis() {
  try {
    if (shouldUseRedis()) {
      return await connectRealRedis();
    } else {
      return await connectInMemoryCache();
    }
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw error;
  }
}

async function connectRealRedis() {
  const { createClient } = require('redis');

  const redisConfig = {
    socket: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
    },
    password: process.env.REDIS_PASSWORD || undefined,
    database: parseInt(process.env.REDIS_DB) || 0,
  };

  redisClient = createClient(redisConfig);

  redisClient.on('error', (err) => {
    logger.error('Redis Client Error:', err);
  });

  redisClient.on('connect', () => {
    logger.info('Redis client connected');
  });

  redisClient.on('ready', () => {
    logger.info('Redis client ready');
  });

  redisClient.on('end', () => {
    logger.info('Redis client disconnected');
  });

  await redisClient.connect();

  // Test the connection
  await redisClient.ping();
  logger.info('Redis connection established successfully');

  return redisClient;
}

async function connectInMemoryCache() {
  logger.info('Using in-memory cache for testing (Redis replacement)');
  inMemoryCache = new Map();
  return { inMemory: true };
}

function getRedisClient() {
  if (shouldUseRedis() && redisClient) {
    return redisClient;
  } else {
    return { inMemory: true };
  }
}

async function closeRedis() {
  if (redisClient && redisClient.isOpen) {
    await redisClient.quit();
    logger.info('Redis connection closed');
  } else {
    logger.info('In-memory cache cleared');
    inMemoryCache.clear();
  }
}

// Cache utility functions that work with both Redis and in-memory storage
async function setCache(key, value, expireInSeconds = 3600) {
  try {
    if (shouldUseRedis() && redisClient) {
      await redisClient.setEx(key, expireInSeconds, JSON.stringify(value));
    } else {
      const expireAt = Date.now() + (expireInSeconds * 1000);
      inMemoryCache.set(key, { value, expireAt });
    }
    return true;
  } catch (error) {
    logger.error('Failed to set cache:', error);
    return false;
  }
}

async function getCache(key) {
  try {
    if (shouldUseRedis() && redisClient) {
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } else {
      const cached = inMemoryCache.get(key);
      if (!cached) return null;

      // Check if expired
      if (Date.now() > cached.expireAt) {
        inMemoryCache.delete(key);
        return null;
      }

      return cached.value;
    }
  } catch (error) {
    logger.error('Failed to get cache:', error);
    return null;
  }
}

async function deleteCache(key) {
  try {
    if (shouldUseRedis() && redisClient) {
      await redisClient.del(key);
    } else {
      inMemoryCache.delete(key);
    }
    return true;
  } catch (error) {
    logger.error('Failed to delete cache:', error);
    return false;
  }
}

async function incrementCounter(key, expireInSeconds = 3600) {
  try {
    if (shouldUseRedis() && redisClient) {
      const count = await redisClient.incr(key);
      if (count === 1) {
        await redisClient.expire(key, expireInSeconds);
      }
      return count;
    } else {
      const cached = inMemoryCache.get(key);
      let count = 1;

      if (cached && Date.now() <= cached.expireAt) {
        count = cached.value + 1;
      }

      const expireAt = Date.now() + (expireInSeconds * 1000);
      inMemoryCache.set(key, { value: count, expireAt });
      return count;
    }
  } catch (error) {
    logger.error('Failed to increment counter:', error);
    return 0;
  }
}

module.exports = {
  connectRedis,
  getRedisClient,
  closeRedis,
  setCache,
  getCache,
  deleteCache,
  incrementCounter
};
