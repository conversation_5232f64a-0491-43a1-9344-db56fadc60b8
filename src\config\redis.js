const logger = require('../utils/logger');

// In-memory cache for testing
let inMemoryCache = new Map();

async function connectRedis() {
  try {
    logger.info('Using in-memory cache for testing (Redis replacement)');
    inMemoryCache = new Map();
    return { inMemory: true };
  } catch (error) {
    logger.error('Failed to initialize in-memory cache:', error);
    throw error;
  }
}

function getRedisClient() {
  return { inMemory: true };
}

async function closeRedis() {
  logger.info('In-memory cache cleared');
  inMemoryCache.clear();
}

// Cache utility functions using in-memory storage
async function setCache(key, value, expireInSeconds = 3600) {
  try {
    const expireAt = Date.now() + (expireInSeconds * 1000);
    inMemoryCache.set(key, { value, expireAt });
    return true;
  } catch (error) {
    logger.error('Failed to set cache:', error);
    return false;
  }
}

async function getCache(key) {
  try {
    const cached = inMemoryCache.get(key);
    if (!cached) return null;

    // Check if expired
    if (Date.now() > cached.expireAt) {
      inMemoryCache.delete(key);
      return null;
    }

    return cached.value;
  } catch (error) {
    logger.error('Failed to get cache:', error);
    return null;
  }
}

async function deleteCache(key) {
  try {
    inMemoryCache.delete(key);
    return true;
  } catch (error) {
    logger.error('Failed to delete cache:', error);
    return false;
  }
}

async function incrementCounter(key, expireInSeconds = 3600) {
  try {
    const cached = inMemoryCache.get(key);
    let count = 1;

    if (cached && Date.now() <= cached.expireAt) {
      count = cached.value + 1;
    }

    const expireAt = Date.now() + (expireInSeconds * 1000);
    inMemoryCache.set(key, { value: count, expireAt });
    return count;
  } catch (error) {
    logger.error('Failed to increment counter:', error);
    return 0;
  }
}

module.exports = {
  connectRedis,
  getRedisClient,
  closeRedis,
  setCache,
  getCache,
  deleteCache,
  incrementCounter
};
